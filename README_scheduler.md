# A股数据定时采集脚本

## 功能说明

这个脚本会自动定时执行 `dabanke_success.py` 和 `wencai_formatted.py` 两个数据采集脚本：

- **开盘时间**（周一至周五 9:30-11:30, 13:00-15:00）：每 **5分钟** 执行一次
- **收盘时间**（其他时间）：每 **30分钟** 执行一次

## 文件说明

- `stock_data_scheduler.py` - 主要的定时调度脚本（包含依赖检查和启动功能）
- `dabanke_success.py` - 大板客数据采集脚本（需要已存在）
- `wencai_formatted.py` - 问财数据采集脚本（需要已存在）

## 安装依赖

确保安装了以下Python包：

```bash
pip install requests beautifulsoup4 pywencai pandas
```

## 使用方法

### 直接运行脚本

```bash
python stock_data_scheduler.py
```

脚本会自动检查依赖并启动定时任务。

## 运行效果

脚本启动后会：

1. **立即执行一次** 数据采集
2. 根据当前时间自动判断执行间隔：
   - 交易时间：每5分钟执行
   - 非交易时间：每30分钟执行
3. 在控制台和日志文件中显示执行情况

### 示例输出

```
🚀 A股数据定时采集脚本
==================================================
✅ 所有必要文件检查通过
✅ 所有Python包依赖检查通过

🎯 准备启动定时采集脚本...
📋 执行规则:
   - 交易时间(9:30-11:30, 13:00-15:00): 每5分钟执行一次
   - 非交易时间: 每30分钟执行一次
   - 按 Ctrl+C 可以优雅退出

==================================================
🎯 A股数据定时采集脚本启动
📋 执行规则:
   - 交易时间(9:30-11:30, 13:00-15:00): 每5分钟执行一次
   - 非交易时间: 每30分钟执行一次
   - 仅在工作日的交易时间内按5分钟间隔执行
============================================================

🚀 开始执行数据采集任务 - 交易时间
📅 执行时间: 2024-01-15 10:30:00
开始执行 dabanke_success.py...
✅ dabanke_success.py 执行完成
开始执行 wencai_formatted.py...
✅ wencai_formatted.py 执行完成
📊 执行结果: 2/2 个脚本成功执行
⏰ 下次执行时间: 10:35:00 (间隔: 5分钟)
```

## 日志记录

- 日志文件保存在 `logs/` 目录下
- 按日期命名：`scheduler_2024-01-15.log`
- 记录所有执行情况和错误信息

## 停止脚本

按 `Ctrl+C` 可以优雅退出脚本。

## 时间规则详解

### A股交易时间
- **周一至周五**
- **上午**：9:30 - 11:30
- **下午**：13:00 - 15:00

### 执行间隔
- **交易时间内**：每5分钟执行一次
  - 例如：9:30, 9:35, 9:40, ..., 11:25, 11:30
  - 例如：13:00, 13:05, 13:10, ..., 14:55, 15:00

- **非交易时间**：每30分钟执行一次
  - 包括：周末、节假日、早盘前、午休、收盘后

## 错误处理

- 如果某个脚本执行失败，不会影响另一个脚本的执行
- 所有错误都会记录在日志文件中
- 网络错误或其他异常不会导致整个程序崩溃

## 注意事项

1. **确保网络连接正常**，因为两个脚本都需要访问网络获取数据
2. **保持脚本运行**，建议在服务器或稳定的环境中运行
3. **定期检查日志**，了解执行情况和可能的错误
4. **磁盘空间**，数据文件和日志文件会占用一定空间

## 自定义配置

如需修改执行间隔，可以编辑 `stock_data_scheduler.py` 中的以下参数：

```python
# 交易时间间隔（秒）
TRADING_INTERVAL = 300  # 5分钟

# 非交易时间间隔（秒）  
NON_TRADING_INTERVAL = 1800  # 30分钟
```

## 故障排除

### 常见问题

1. **模块导入失败**
   - 检查 `dabanke_success.py` 和 `wencai_formatted.py` 是否在同一目录
   - 检查文件是否有语法错误

2. **网络连接问题**
   - 检查网络连接
   - 检查防火墙设置

3. **权限问题**
   - 确保有写入日志文件的权限
   - 确保有创建目录的权限

### 查看详细错误

查看日志文件获取详细错误信息：

```bash
tail -f logs/scheduler_$(date +%Y-%m-%d).log
```
