# 股票数据交易时间保存功能说明

## 功能概述

为 `dabanke_success.py` 和 `wencai_formatted.py` 两个脚本都添加了交易时间保存功能。在原有保存方式不变的基础上，新增了交易时间段的额外保存功能。

## 新增功能

### 1. 交易时间检测
- **上午交易时间**: 9:30 - 11:30
- **下午交易时间**: 11:30 - 15:00
- 只有在这两个时间段内运行脚本时，才会触发额外保存

### 2. 日期文件夹保存
- 在 `dabanke_data` 目录下创建当天日期的文件夹（格式：YYYY-MM-DD）
- 例如：`dabanke_data/2025-08-13/`

### 3. 时间戳文件名
- 文件名包含具体时间到秒
- 格式：`dabanke_data_YYYY-MM-DD_HH-MM-SS.json`
- 例如：`dabanke_data_2025-08-13_09-30-15.json`

## 保存逻辑

**重要说明**: 现在是**双重保存**机制，无论什么时候运行脚本都会保存两份：

1. **原有保存方式**（始终保存）
2. **新增时间戳保存**（始终保存，但在交易时间内会有特殊标识）

### 大板客数据保存方式

#### 1. 原有保存方式（始终保存）
```
dabanke_data/dabanke_data_2025-08-13.json
```

#### 2. 新增时间戳保存（始终保存）
```
dabanke_data/2025-08-13/dabanke_data_2025-08-13_09-30-15.json
dabanke_data/2025-08-13/dabanke_data_2025-08-13_10-15-30.json
dabanke_data/2025-08-13/dabanke_data_2025-08-13_14-45-20.json
dabanke_data/2025-08-13/dabanke_data_2025-08-13_16-30-00.json  # 非交易时间也会保存
```

### 问财数据保存方式

#### 1. 原有保存方式（始终保存）
```
market_data/market_data_2025-08-13.json
```

#### 2. 新增时间戳保存（始终保存）
```
market_data/2025-08-13/market_data_2025-08-13_09-30-15.json
market_data/2025-08-13/market_data_2025-08-13_10-15-30.json
market_data/2025-08-13/market_data_2025-08-13_14-45-20.json
market_data/2025-08-13/market_data_2025-08-13_16-30-00.json  # 非交易时间也会保存
```

## 使用方法

### 大板客数据脚本

#### 正常运行
```bash
python dabanke_success.py
```

#### 测试功能
```bash
python dabanke_success.py test
```

### 问财数据脚本

#### 正常运行
```bash
python wencai_formatted.py
```

#### 测试功能
```bash
python wencai_formatted.py test
```

## 代码变更说明

### 新增函数

1. **`is_trading_time()`**: 检查当前时间是否在交易时间内
2. **`save_trading_time_data(data, base_filename)`**: 在交易时间内保存数据到日期文件夹
3. **`test_trading_time_save()`**: 测试交易时间保存功能

### 修改位置

#### dabanke_success.py
1. **导入模块**: 新增 `time` 模块
2. **main() 函数**: 在保存数据后调用 `save_trading_time_data()`
3. **get_dabanke_data_for_date() 函数**: 在保存数据后调用 `save_trading_time_data()`

#### wencai_formatted.py
1. **导入模块**: 新增 `time` 模块
2. **save_market_data_json() 函数**: 在保存数据后调用 `save_trading_time_data()`

## 优势

1. **完整备份**: 每次运行都会保存两份数据，确保数据安全
2. **时间追踪**: 可以精确追踪每次数据获取的时间到秒
3. **历史分析**: 便于分析同一天内不同时间点的数据变化
4. **兼容性**: 原有功能完全保持不变
5. **灵活性**: 无论交易时间内外都会保存，方便测试和调试

## 注意事项

- **双重保存**: 每次运行都会保存两份文件（原有位置 + 时间戳文件夹）
- **交易时间标识**: 在交易时间内运行会显示"交易时间数据已保存"，非交易时间显示"(非交易时间)"
- **文件名格式**: 时间戳文件使用 `-` 分隔符以确保跨平台兼容性
- **存储空间**: 由于双重保存，会占用更多存储空间
