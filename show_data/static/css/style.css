/* 股票数据可视化样式 */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
}

.container-fluid {
    padding: 10px 10px 0 10px;
    height: 100vh;
    overflow: hidden;
}

/* 标题样式 */
h1,
h2 {
    font-weight: 700;
    margin-bottom: 5px;
}

h6 {
    font-weight: 600;
    font-size: 0.9rem;
}

/* 卡片样式 */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
    margin-bottom: 10px;
}

.card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    font-weight: 600;
}

.card-body {
    padding: 1rem;
}

.chart-card .card-body {
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* 紧凑卡片样式 */
.compact-card {
    height: 120px;
}

.compact-chart-card {
    height: 280px;
}

/* 图表容器 */
canvas {
    max-height: 200px;
}

/* 紧凑图表容器 */
.chart-container-small {
    position: relative;
    height: 200px;
    width: 100%;
}

/* 数据概览样式 */
.summary-item {
    text-align: center;
    padding: 5px;
    margin: 2px 0;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.summary-value {
    font-size: 1.1rem;
    font-weight: bold;
    color: #495057;
}

.summary-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 2px;
}

/* 状态指示器 */
.status-positive {
    color: #28a745;
}

.status-negative {
    color: #dc3545;
}

.status-neutral {
    color: #6c757d;
}

/* 加载动画 */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 5px;
    }

    .card-body {
        padding: 0.5rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .compact-card {
        height: 100px;
    }

    .compact-chart-card {
        height: 220px;
    }

    .chart-container-small {
        height: 150px;
    }

    .pyramid-visual-container {
        height: 180px;
        padding: 5px;
    }

    /* 移动端金字塔尺寸调整 */
    .pyramid-level-9进10 {
        width: 30px;
        height: 16px;
        font-size: 0.6rem;
    }

    .pyramid-level-5进6 {
        width: 45px;
        height: 18px;
        font-size: 0.6rem;
    }

    .pyramid-level-4进5 {
        width: 50px;
        height: 19px;
        font-size: 0.6rem;
    }

    .pyramid-level-3进4 {
        width: 60px;
        height: 20px;
        font-size: 0.6rem;
    }

    .pyramid-level-2进3 {
        width: 75px;
        height: 22px;
        font-size: 0.6rem;
    }

    .pyramid-level-1进2 {
        width: 90px;
        height: 24px;
        font-size: 0.6rem;
    }

    .pyramid-level-首板 {
        width: 105px;
        height: 26px;
        font-size: 0.6rem;
    }

    .success-rate {
        font-size: 0.5rem;
        padding: 1px 2px;
    }

    .success-rate-left {
        left: -35px;
    }

    .success-rate-right {
        right: -35px;
    }

    .stock-count {
        font-size: 0.5rem;
    }

    /* 移动端调整 */
    .pyramid-card {
        height: 300px;
    }

    .pyramid-container {
        height: 240px;
        padding: 10px 5px;
    }

    .summary-card {
        min-height: 80px;
        margin-top: 8px;
    }

    .chart-card {
        flex: 1;
        min-height: 0;
    }

    .market-overview-corner {
        position: relative;
        top: 0;
        right: 0;
        width: 100%;
        margin-bottom: 10px;
    }

    .chart-container-medium {
        height: 100px;
    }

    /* 移动端金字塔尺寸 - 更长更扁 */
    .pyramid-level-9进10 {
        width: 80px;
        min-height: 25px;
        font-size: 0.6rem;
    }

    .pyramid-level-5进6 {
        width: 120px;
        min-height: 28px;
        font-size: 0.65rem;
    }

    .pyramid-level-4进5 {
        width: 140px;
        min-height: 30px;
        font-size: 0.65rem;
    }

    .pyramid-level-3进4 {
        width: 160px;
        min-height: 32px;
        font-size: 0.7rem;
    }

    .pyramid-level-2进3 {
        width: 200px;
        min-height: 36px;
        font-size: 0.75rem;
    }

    .pyramid-level-1进2 {
        width: 240px;
        min-height: 40px;
        font-size: 0.8rem;
    }

    .pyramid-level-首板 {
        width: 280px;
        min-height: 44px;
        font-size: 0.85rem;
    }

    .success-rate-left {
        left: -55px;
    }

    .success-rate-right {
        right: -55px;
    }

    /* 移动端新布局调整 */
    .pyramid-card-new {
        height: 400px;
    }

    .charts-container {
        height: auto;
        overflow: visible;
    }

    .chart-card {
        flex: 1;
        min-height: 0;
    }

    .market-overview-corner {
        position: relative;
        top: 0;
        right: 0;
        width: 100%;
        margin-bottom: 10px;
    }

    .pyramid-layout,
    .pyramid-layout-fullwidth {
        height: 320px;
        gap: 10px;
    }

    .pyramid-container-fullwidth {
        height: 290px;
    }

    /* 移动端内联市场概况 */
    .summary-card-inline {
        margin-bottom: 10px;
    }

    .market-bars-inline,
    .limit-bars-inline {
        gap: 1px;
        font-size: 0.7rem;
    }

    .pyramid-labels-left,
    .pyramid-success-rates {
        height: 250px;
        gap: 6px;
    }

    .pyramid-label,
    .success-rate-item {
        padding: 6px 8px;
        font-size: 0.75rem;
        min-width: 50px;
    }

    .pyramid-container-new {
        height: 250px;
        padding: 10px;
    }

    .market-overview-corner {
        position: relative;
        top: 0;
        right: 0;
        width: 100%;
        margin-bottom: 15px;
    }

    .summary-card-corner {
        min-height: 100px;
    }

    .stock-mini {
        font-size: 0.5rem;
        padding: 1px 3px;
        max-width: 35px;
    }

    .pyramid-level-9进10 {
        width: 80px;
        height: 25px;
        font-size: 0.6rem;
        clip-path: polygon(50% 0%, 50% 0%, 100% 100%, 0% 100%);
        /* 尖三角形 */
    }

    .pyramid-level-5进6 {
        width: 120px;
        height: 28px;
        font-size: 0.65rem;
        clip-path: polygon(25% 0%, 75% 0%, 100% 100%, 0% 100%);
    }

    .pyramid-level-4进5 {
        width: 140px;
        height: 30px;
        font-size: 0.65rem;
        clip-path: polygon(22% 0%, 78% 0%, 100% 100%, 0% 100%);
    }

    .pyramid-level-3进4 {
        width: 160px;
        height: 32px;
        font-size: 0.7rem;
        clip-path: polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%);
    }

    .pyramid-level-2进3 {
        width: 200px;
        height: 36px;
        font-size: 0.75rem;
        clip-path: polygon(15% 0%, 85% 0%, 100% 100%, 0% 100%);
    }

    .pyramid-level-1进2 {
        width: 240px;
        height: 40px;
        font-size: 0.8rem;
        clip-path: polygon(10% 0%, 90% 0%, 100% 100%, 0% 100%);
    }

    .pyramid-level-首板 {
        width: 280px;
        height: 44px;
        font-size: 0.85rem;
        clip-path: polygon(5% 0%, 95% 0%, 100% 100%, 0% 100%);
    }

    .success-rate {
        font-size: 0.55rem;
        padding: 2px 4px;
    }

    .success-rate-left {
        left: -45px;
    }

    .success-rate-right {
        right: -45px;
    }

    .stock-mini {
        font-size: 0.45rem;
        padding: 1px 2px;
        max-width: 30px;
    }

    .level-stocks {
        max-height: 20px;
        gap: 1px;
    }
}

/* 图表工具提示样式 */
.chart-tooltip {
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
}

/* 图例样式 */
.chart-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px 5px 0;
    font-size: 12px;
}

.legend-color {
    width: 12px;
    height: 12px;
    margin-right: 5px;
    border-radius: 2px;
}

/* 数据表格样式 */
.data-table {
    font-size: 0.9rem;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-top: none;
}

.data-table td {
    vertical-align: middle;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    border-radius: 4px;
}

/* 徽章样式 */
.badge {
    font-size: 0.8rem;
}

/* 市场概览右上角样式 */
.market-overview-corner {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 280px;
    z-index: 100;
}

.summary-card-corner {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 紧凑型市场条形图 */
.market-bars-compact,
.limit-bars-compact {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.market-bar-row,
.limit-bar-row {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.bar-container {
    flex: 1;
    height: 16px !important;
    min-height: 16px !important;
    background: #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    display: flex;
}

.bar-segment {
    height: 100%;
    transition: width 0.3s ease;
}

.bar-segment.rise {
    background: linear-gradient(90deg, #dc3545, #ff6b7a);
}

.bar-segment.fall {
    background: linear-gradient(90deg, #28a745, #5cb85c);
}

.bar-segment.flat {
    background: linear-gradient(90deg, #6c757d, #adb5bd);
}

.bar-segment.limit-up {
    background: linear-gradient(90deg, #dc3545, #ff4757);
}

.bar-segment.limit-down {
    background: linear-gradient(90deg, #28a745, #2ed573);
}

.bar-label {
    min-width: 60px;
    font-weight: 500;
    text-align: right;
}

.bar-label.rise {
    color: #dc3545;
}

.bar-label.fall {
    color: #28a745;
}

.bar-label.flat {
    color: #6c757d;
}

.volume-info-compact {
    text-align: center;
    padding: 8px;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 6px;
}

.volume-amount {
    font-size: 16px;
    font-weight: bold;
    color: #007bff;
}

.volume-label {
    font-size: 11px;
    color: #6c757d;
    margin-top: 2px;
}

/* 页脚样式 */
footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: rgba(248, 249, 250, 0.95);
    border-top: 1px solid #dee2e6;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 确保页面在一屏内显示 */
html,
body {
    height: 100%;
    overflow: hidden;
}

.container-fluid {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* 小字体样式 */
.small {
    font-size: 0.8rem;
}

/* 紧凑间距 */
.mb-2 {
    margin-bottom: 0.5rem !important;
}

.py-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
}

.py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

/* 图表切换按钮样式 */
.btn-group-sm .btn {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 0.2rem;
}

.btn-outline-primary.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-outline-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
}

/* 新的金字塔卡片样式 */
.pyramid-card-new {
    height: calc(100vh - 160px);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

/* 横向布局大盘概况样式 */
.summary-card-horizontal {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    background: white;
    flex-shrink: 0;
    height: 80px;
    max-width: 420px;
    margin-left: auto;
}

/* 大盘标题 */
.market-title {
    width: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-radius: 6px;
    height: 64px;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    font-size: 0.75rem;
    font-weight: 600;
    padding: 4px 2px;
}

/* 横条区域 */
.market-bars-area {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: stretch;
    min-width: 0;
    flex: 1;
    max-width: 280px;
    gap: 2px;
}

/* 成交金额侧边 */
.volume-info-side {
    width: 65px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 6px;
    height: 64px;
    border: 1px solid #cbd5e1;
}

.market-bars-horizontal,
.limit-bars-horizontal {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.65rem;
    margin-bottom: 6px;
    height: 24px;
}

.bar-container-horizontal {
    width: 160px;
    display: flex;
    height: 10px;
    border-radius: 5px;
    overflow: hidden;
    background: #f1f5f9;
    position: relative;
}

.bar-segment-horizontal {
    height: 100%;
    transition: width 0.3s ease;
}

.bar-segment-horizontal.rise {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.bar-segment-horizontal.fall {
    background: linear-gradient(90deg, #22c55e, #16a34a);
}

.bar-segment-horizontal.flat {
    background: linear-gradient(90deg, #94a3b8, #64748b);
}

.bar-segment-horizontal.limit-up {
    background: linear-gradient(90deg, #dc2626, #b91c1c);
}

.bar-segment-horizontal.limit-down {
    background: linear-gradient(90deg, #16a34a, #15803d);
}

.bar-label-left,
.bar-label-right {
    font-size: 0.6rem;
    font-weight: 600;
    padding: 1px 3px;
    border-radius: 2px;
    color: white;
    min-width: 35px;
    text-align: center;
    white-space: nowrap;
    flex-shrink: 0;
}

.bar-label-left.rise,
.bar-label-right.rise {
    background: #ef4444;
}

.bar-label-left.fall,
.bar-label-right.fall {
    background: #22c55e;
}

.volume-value {
    font-size: 0.8rem;
    font-weight: bold;
    color: #1e40af;
    margin-bottom: 2px;
}

.volume-label {
    font-size: 0.55rem;
    color: #64748b;
}

/* 右上角市场概况 */
.market-overview-corner {
    position: absolute;
    top: 10px;
    right: 15px;
    z-index: 1000;
    width: 260px;
    pointer-events: none;
}

.market-overview-corner .card {
    pointer-events: auto;
}

.summary-card-corner {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    min-height: 120px;
}

/* 金字塔布局容器 */
.pyramid-layout {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 550px;
    gap: 20px;
}

/* 全宽度金字塔布局 */
.pyramid-layout-fullwidth {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    padding: 0 15px;
}

/* 左侧标签 */
.pyramid-labels-left {
    display: flex;
    flex-direction: column-reverse;
    justify-content: center;
    height: 480px;
    gap: 12px;
}

.pyramid-label {
    padding: 8px 12px;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    color: #475569;
    text-align: center;
    min-width: 60px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 右侧成功率 */
.pyramid-success-rates {
    display: flex;
    flex-direction: column-reverse;
    justify-content: center;
    height: 480px;
    gap: 12px;
}

.success-rate-item {
    padding: 8px 12px;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 1px solid #f59e0b;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    color: #92400e;
    text-align: center;
    min-width: 60px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.rate-value {
    display: block;
    font-size: 0.9rem;
    font-weight: bold;
}

/* 紧凑版市场概况样式 */
.market-bars-compact,
.limit-bars-compact {
    margin-bottom: 8px;
}

.market-bars-compact .data-bar-header,
.limit-bars-compact .data-bar-header {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    margin-bottom: 4px;
    font-weight: 600;
}

.market-bars-compact .data-bar,
.limit-bars-compact .data-bar {
    height: 12px;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.volume-info-compact {
    text-align: center;
    padding: 4px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.volume-info-compact .volume-amount {
    font-size: 1.2rem;
    font-weight: bold;
    color: #1f2937;
    line-height: 1.2;
}

.volume-info-compact .volume-label {
    font-size: 0.7rem;
    color: #6b7280;
    margin-top: 2px;
}

/* 纯净版金字塔样式 */
.level-content-clean {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 8px 12px;
}

.level-stocks-clean {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    justify-content: center;
    align-items: flex-start;
    max-width: 100%;
    max-height: 100%;
    overflow: hidden;
    padding: 4px;
}

.stock-mini-clean {
    display: inline-block;
    padding: 1px 4px;
    margin: 1px;
    border-radius: 2px;
    font-size: 0.65rem;
    font-weight: 500;
    color: white;
    background: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    line-height: 1.2;
}

.stock-mini-clean:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stock-mini-clean.success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stock-mini-clean.fail {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.more-indicator-clean {
    display: inline-block;
    padding: 2px 6px;
    margin: 1px;
    border-radius: 3px;
    font-size: 0.65rem;
    color: #6b7280;
    background: rgba(107, 114, 128, 0.1);
    border: 1px dashed #6b7280;
}

/* 图表容器样式 */
.charts-container {
    height: calc(100vh - 160px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

/* 图表卡片样式 */
.chart-card {
    flex: 1 !important;
    min-height: 0 !important;
    height: auto !important;
    margin-bottom: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 概览区域样式 */
.summary-section {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
}

.summary-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.summary-title {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 图表容器 */
.chart-container-medium {
    position: relative;
    flex: 1;
    width: 100%;
    min-height: 120px;
}

/* 新的金字塔容器样式 */
.pyramid-container-new {
    position: relative;
    height: 480px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 全宽度金字塔容器样式 */
.pyramid-container-fullwidth {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    overflow: visible;
}

/* 连板文本显示样式 */
.lianban-text-display {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    font-family: 'Microsoft YaHei', sans-serif;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.lianban-level-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 4px;
    padding: 4px 0;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s;
    flex: 1;
    min-height: 60px;
}

.lianban-level-row:hover {
    background-color: #f1f3f4;
    border-radius: 4px;
}

/* 级别标签样式优化 */
.lianban-level-label {
    flex-shrink: 0;
    min-width: 60px;
}

/* 股票列表样式优化 */
.lianban-stocks-list {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.lianban-level-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.level-info {
    display: flex;
    align-items: center;
    min-width: 80px;
    margin-right: 10px;
    flex-shrink: 0;
}

.level-badge {
    display: inline-block;
    padding: 3px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    color: white;
    margin-right: 6px;
    min-width: 35px;
    text-align: center;
}

.level-3-4 {
    background-color: #f59e0b;
}

.level-2-3 {
    background-color: #3b82f6;
}

.level-1-2 {
    background-color: #10b981;
}

.level-首板 {
    background-color: #f97316;
}

.level-4-5 {
    background-color: #8b5cf6;
}

.level-5-6 {
    background-color: #ef4444;
}

.level-stats {
    font-size: 10px;
    color: #6b7280;
    font-weight: 500;
}

.stocks-list {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2px;
    overflow: visible;
    position: relative;
    padding: 2px;
}

.stocks-list.expanded {
    overflow: visible;
}

.stock-item {
    display: block;
    padding: 1px 3px;
    border-radius: 2px;
    font-size: 9px;
    cursor: pointer;
    transition: all 0.2s;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid transparent;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    min-height: 18px;
    line-height: 16px;
}

.stock-item:hover {
    background-color: white;
    border-color: #ddd;
    transform: translateY(-1px);
    overflow: visible;
    z-index: 10;
    position: relative;
    white-space: normal;
    text-overflow: unset;
}

.stock-item.text-success {
    color: #059669;
    background-color: rgba(5, 150, 105, 0.1);
}

.stock-item.text-danger {
    color: #dc2626;
    background-color: rgba(220, 38, 38, 0.1);
}

.stock-item.text-warning {
    color: #d97706;
    background-color: rgba(217, 119, 6, 0.1);
}

/* 金字塔卡片高度控制 - 与右边所有模块齐平 */
.pyramid-card-new .card-body {
    height: calc(100% - 60px);
    overflow: hidden;
}

.pyramid-triangle {
    position: relative;
    width: 0;
    height: 0;
    border-left: 150px solid transparent;
    border-right: 150px solid transparent;
    border-bottom: 400px solid #e9ecef;
    margin: 20px auto;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.pyramid-content {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 280px;
    height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

.pyramid-level {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 8px 12px;
    margin: 0;
    border-radius: 0;
    color: white;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
}

.pyramid-level:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    z-index: 10;
}

/* 金字塔各层级尺寸和颜色 - 连贯的金字塔形状 */
/* 4进5是顶部（尖三角形） */
.pyramid-level-4进5 {
    width: 70px;
    height: 55px;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white !important;
    font-size: 0.65rem;
    clip-path: polygon(50% 0%, 50% 0%, 100% 100%, 0% 100%);
    margin-bottom: -1px;
    /* 尖三角形顶部，底部宽度70px */
}

/* 3进4是第二层 */
.pyramid-level-3进4 {
    width: 110px;
    height: 65px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: #000 !important;
    font-size: 0.7rem;
    clip-path: polygon(32% 0%, 68% 0%, 100% 100%, 0% 100%);
    margin-bottom: -1px;
    /* 顶部宽度约70px，底部宽度110px */
}

.pyramid-level-2进3 {
    width: 200px;
    height: 85px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    font-size: 0.8rem;
    clip-path: polygon(27.5% 0%, 72.5% 0%, 100% 100%, 0% 100%);
    margin-bottom: -1px;
    /* 顶部宽度约110px，底部宽度200px */
}

.pyramid-level-1进2 {
    width: 320px;
    height: 100px;
    background: linear-gradient(135deg, #10b981, #059669);
    font-size: 0.85rem;
    clip-path: polygon(18.75% 0%, 81.25% 0%, 100% 100%, 0% 100%);
    margin-bottom: -1px;
    /* 顶部宽度约200px，底部宽度320px */
}

.pyramid-level-首板 {
    width: 100%;
    max-width: 420px;
    height: 130px;
    background: linear-gradient(135deg, #f97316, #ea580c);
    font-size: 0.9rem;
    clip-path: polygon(16.5% 0%, 83.5% 0%, 100% 100%, 0% 100%);
    border-radius: 0 0 8px 8px;
    /* 顶部宽度约300px (67%*420px≈280px)，底部铺满 */
}

/* 隐藏不存在的级别 */
.pyramid-level-9进10,
.pyramid-level-8进9,
.pyramid-level-7进8,
.pyramid-level-6进7,
.pyramid-level-5进6 {
    display: none;
}

/* 级别标题 */
.level-title {
    font-weight: 700;
    margin-bottom: 4px;
    font-size: 0.8rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 股票显示区域 */
.level-stocks {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2px;
    max-height: none;
    width: 100%;
    padding: 0 4px;
}

.stock-mini {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 4px;
    padding: 2px 4px;
    font-size: 0.55rem;
    font-weight: 600;
    white-space: nowrap;
    max-width: 45px;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
}

.stock-mini:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
}

.stock-mini.success {
    background: rgba(34, 197, 94, 0.2);
    border-color: rgba(34, 197, 94, 0.4);
    box-shadow: 0 0 4px rgba(34, 197, 94, 0.3);
}

.stock-mini.fail {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.4);
    box-shadow: 0 0 4px rgba(239, 68, 68, 0.3);
}

/* 成功率显示 */
.success-rate {
    position: absolute;
    font-size: 0.65rem;
    font-weight: 700;
    color: #333;
    background: rgba(255, 255, 255, 0.95);
    padding: 3px 6px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.success-rate-left {
    left: -65px;
    top: 50%;
    transform: translateY(-50%);
}

.success-rate-right {
    right: -65px;
    top: 50%;
    transform: translateY(-50%);
}

/* 数据条样式 */
.market-bars-container,
.limit-bars-container {
    margin-bottom: 16px;
}

.data-bar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 0.8rem;
    font-weight: 600;
}

.data-bar {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    background: #f1f5f9;
    display: flex;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.bar-segment {
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
}

.bar-segment:first-child {
    border-radius: 4px 0 0 4px;
}

.bar-segment:last-child {
    border-radius: 0 4px 4px 0;
}

.bar-segment.single {
    border-radius: 4px;
}

/* 涨跌数据条 */
.rise-segment {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.fall-segment {
    background: linear-gradient(90deg, #22c55e, #16a34a);
}

.flat-segment {
    background: linear-gradient(90deg, #6b7280, #4b5563);
}

/* 涨跌停数据条 */
.limit-up-segment {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.limit-down-segment {
    background: linear-gradient(90deg, #22c55e, #16a34a);
}

/* 成交金额信息 */
.volume-info {
    text-align: center;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.volume-amount {
    font-size: 1rem;
    font-weight: 700;
    color: #1e293b;
}

.volume-label {
    font-size: 0.7rem;
    color: #64748b;
    margin-top: 2px;
}

.stock-chip {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 0.7rem;
    white-space: nowrap;
    border: 1px solid;
    min-width: 0;
    max-width: 120px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.stock-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stock-chip-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.stock-chip-fail {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.stock-name {
    font-weight: 600;
    margin-right: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.stock-change {
    font-size: 0.65rem;
    opacity: 0.8;
}

/* 金字塔级别颜色 */
.level-9进10 {
    background-color: #dc3545;
}

.level-5进6 {
    background-color: #6f42c1;
}

.level-4进5 {
    background-color: #8b5cf6;
    color: white !important;
}

.level-3进4 {
    background-color: #ffc107;
    color: #000 !important;
}

.level-2进3 {
    background-color: #007bff;
}

.level-1进2 {
    background-color: #28a745;
}

.level-首板 {
    background-color: #fd7e14;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 自定义颜色 */
.text-success-custom {
    color: #20c997 !important;
}

.text-danger-custom {
    color: #fd7e14 !important;
}

.bg-success-custom {
    background-color: #20c997 !important;
}

.bg-danger-custom {
    background-color: #fd7e14 !important;
}

/* 图表容器响应式 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

@media (max-width: 576px) {
    .chart-container {
        height: 250px;
    }
}