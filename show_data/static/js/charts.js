// 股票数据可视化图表脚本

// 全局变量
let lianbanChart, marketChart, limitChart, volumeChart;
let currentLianbanData = null;
let currentLianbanView = 'main';

// 颜色配置
const colors = {
    primary: '#007bff',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40'
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    initializeCharts();
    loadAllData();

    // 设置定时刷新（每5分钟）
    setInterval(loadAllData, 5 * 60 * 1000);
});

// 初始化所有图表
function initializeCharts() {
    // 连板进度图表
    const lianbanCtx = document.getElementById('lianbanChart').getContext('2d');
    lianbanChart = new Chart(lianbanCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        font: {
                            size: 10
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            const dataIndex = context.dataIndex;
                            const datasetIndex = context.datasetIndex;
                            const level = context.dataset.label;

                            // 获取原始数据
                            if (currentLianbanData && currentLianbanData[dataIndex]) {
                                const originalValue = currentLianbanData[dataIndex].progress[level]?.percentage || 0;
                                const successCount = currentLianbanData[dataIndex].progress[level]?.success || 0;
                                const totalCount = currentLianbanData[dataIndex].progress[level]?.total || 0;

                                return `${level}: ${originalValue}% (${successCount}/${totalCount})`;
                            }

                            return `${level}: ${context.parsed.y}%`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    min: 0,
                    ticks: {
                        font: {
                            size: 10
                        },
                        stepSize: 20,
                        callback: function (value) {
                            return value + '%';
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: {
                            size: 9
                        },
                        maxTicksLimit: 15 // 限制X轴标签数量
                    },
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // 市场涨跌分布图表
    const marketCtx = document.getElementById('marketChart').getContext('2d');
    marketChart = new Chart(marketCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        font: {
                            size: 10
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            size: 10
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });

    // 涨跌停统计图表
    const limitCtx = document.getElementById('limitChart').getContext('2d');
    limitChart = new Chart(limitCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        font: {
                            size: 10
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            size: 10
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });

    // 成交金额图表
    const volumeCtx = document.getElementById('volumeChart').getContext('2d');
    volumeChart = new Chart(volumeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        font: {
                            size: 10
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            size: 10
                        },
                        callback: function (value) {
                            return formatAmount(value);
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// 加载所有数据
async function loadAllData() {
    try {
        await Promise.all([
            loadLianbanData(),
            loadMarketData(),
            loadLimitData()
        ]);

        updateLastUpdateTime();
    } catch (error) {
        console.error('加载数据失败:', error);
        showError('数据加载失败，请稍后重试');
    }
}

// 加载连板进度数据
async function loadLianbanData() {
    try {
        const response = await fetch('/api/lianban_progress');
        const result = await response.json();

        if (result.success) {
            updateLianbanChart(result.data);
            updateModernPyramid(result.data, result.latest_stocks);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('连板数据加载失败:', error);
    }
}

// 加载市场数据
async function loadMarketData() {
    try {
        const response = await fetch('/api/market_summary');
        const result = await response.json();

        if (result.success) {
            updateMarketChart(result.data);
            updateVolumeChart(result.data);
            updateMarketBars(result.data);
            // 市场数据中也包含涨跌停信息，直接更新
            updateLimitBars(result.data);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('市场数据加载失败:', error);
    }
}

// 加载涨跌停数据
async function loadLimitData() {
    try {
        const response = await fetch('/api/limit_stats');
        const result = await response.json();

        if (result.success) {
            updateLimitChart(result.data);
            updateLimitBars(result.data);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('涨跌停数据加载失败:', error);
    }
}



// 更新市场图表
function updateMarketChart(data) {
    if (!data || data.length === 0) return;

    const dates = data.map(item => formatDate(item.date));

    marketChart.data.labels = dates;
    marketChart.data.datasets = [
        {
            label: '上涨家数',
            data: data.map(item => item.rise_count || item.up_count || 0),
            borderColor: colors.success,
            backgroundColor: colors.success + '20',
            fill: false
        },
        {
            label: '下跌家数',
            data: data.map(item => item.fall_count || item.down_count || 0),
            borderColor: colors.danger,
            backgroundColor: colors.danger + '20',
            fill: false
        }
    ];
    marketChart.update();
}

// 更新涨跌停图表
function updateLimitChart(data) {
    if (!data || data.length === 0) return;

    const dates = data.map(item => formatDate(item.date));

    limitChart.data.labels = dates;
    limitChart.data.datasets = [
        {
            label: '涨停数量',
            data: data.map(item => item.limit_up || item.limit_up_total || 0),
            backgroundColor: colors.danger,
            borderColor: colors.danger,
            borderWidth: 1
        },
        {
            label: '跌停数量',
            data: data.map(item => item.limit_down || item.limit_down_total || 0),
            backgroundColor: colors.success,
            borderColor: colors.success,
            borderWidth: 1
        }
    ];
    limitChart.update();
}

// 更新成交金额图表
function updateVolumeChart(data) {
    if (!data || data.length === 0) return;

    const dates = data.map(item => formatDate(item.date));

    volumeChart.data.labels = dates;
    volumeChart.data.datasets = [
        {
            label: '成交金额',
            data: data.map(item => item.volume || item.total_amount || 0),
            borderColor: colors.primary,
            backgroundColor: colors.primary + '20',
            fill: true,
            tension: 0.1
        }
    ];
    volumeChart.update();
}

// 工具函数
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return `${date.getMonth() + 1}/${date.getDate()}`;
}

function formatAmount(amount) {
    if (amount >= 1e12) {
        return (amount / 1e12).toFixed(1) + '万亿';
    } else if (amount >= 1e8) {
        return (amount / 1e8).toFixed(1) + '亿';
    } else if (amount >= 1e4) {
        return (amount / 1e4).toFixed(1) + '万';
    }
    return amount.toString();
}

function getColorByIndex(index, alpha = 1) {
    const colorList = [
        `rgba(255, 99, 132, ${alpha})`,
        `rgba(54, 162, 235, ${alpha})`,
        `rgba(255, 205, 86, ${alpha})`,
        `rgba(75, 192, 192, ${alpha})`,
        `rgba(153, 102, 255, ${alpha})`,
        `rgba(255, 159, 64, ${alpha})`
    ];
    return colorList[index % colorList.length];
}

function updateLastUpdateTime() {
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN');
    document.getElementById('last-update').textContent = timeStr;
}

function showError(message) {
    console.error(message);
    // 这里可以添加错误提示UI
}

// 更新连板概览
function updateLianbanSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];
    const progress = latest.progress;

    let html = '<div class="row">';
    Object.keys(progress).forEach(level => {
        const item = progress[level];
        html += `
            <div class="col-4 mb-1">
                <div class="summary-item">
                    <div class="summary-value">${item.percentage}%</div>
                    <div class="summary-label">${level}</div>
                    <small class="text-muted" style="font-size: 0.65rem;">${item.success}/${item.total}</small>
                </div>
            </div>
        `;
    });
    html += '</div>';

    document.getElementById('lianban-summary').innerHTML = html;
}

// 更新市场概览
function updateMarketSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];

    // 计算总数和比例
    const totalStocks = latest.up_count + latest.down_count + (latest.flat_count || 0);

    // 更新涨跌分布 - 带对比横条
    const marketBars = document.getElementById('market-bars');
    if (marketBars) {
        const upPercent = totalStocks > 0 ? (latest.up_count / totalStocks * 100) : 0;
        const downPercent = totalStocks > 0 ? (latest.down_count / totalStocks * 100) : 0;
        const flatPercent = totalStocks > 0 ? ((latest.flat_count || 0) / totalStocks * 100) : 0;

        marketBars.innerHTML = `
            <div class="market-bar-row">
                <span class="bar-label rise">涨 ${latest.up_count}</span>
                <span class="bar-label fall">跌 ${latest.down_count}</span>
            </div>
            <div class="bar-container">
                <div class="bar-segment rise" style="width: ${upPercent}%"></div>
                <div class="bar-segment flat" style="width: ${flatPercent}%"></div>
                <div class="bar-segment fall" style="width: ${downPercent}%"></div>
            </div>
        `;
    }

    // 更新成交金额
    const volumeInfo = document.getElementById('volume-info');
    if (volumeInfo) {
        volumeInfo.innerHTML = `
            <div class="volume-amount">${latest.total_amount_formatted}</div>
            <div class="volume-label">成交金额</div>
        `;
    }
}

// 更新涨跌停概览
function updateLimitSummary(data) {
    if (!data || data.length === 0) return;

    const latest = data[data.length - 1];

    // 更新涨跌停分布 - 带对比横条
    const limitBars = document.getElementById('limit-bars');
    if (limitBars) {
        const totalLimits = latest.limit_up_total + latest.limit_down_total;

        if (totalLimits > 0) {
            const limitUpPercent = (latest.limit_up_total / totalLimits * 100);
            const limitDownPercent = (latest.limit_down_total / totalLimits * 100);

            limitBars.innerHTML = `
                <div class="limit-bar-row">
                    <span class="bar-label rise">涨停 ${latest.limit_up_total}</span>
                    <span class="bar-label fall">跌停 ${latest.limit_down_total}</span>
                </div>
                <div class="bar-container">
                    <div class="bar-segment limit-up" style="width: ${limitUpPercent}%"></div>
                    <div class="bar-segment limit-down" style="width: ${limitDownPercent}%"></div>
                </div>
            `;
        } else {
            limitBars.innerHTML = `
                <div class="limit-bar-row">
                    <span class="bar-label rise">涨停 ${latest.limit_up_total}</span>
                    <span class="bar-label fall">跌停 ${latest.limit_down_total}</span>
                </div>
                <div class="bar-container">
                    <div class="bar-segment limit-up" style="width: 50%"></div>
                    <div class="bar-segment limit-down" style="width: 50%"></div>
                </div>
            `;
        }
    }
}

// 更新连板进度图表
function updateLianbanChart(data) {
    if (!data || data.length === 0) return;

    currentLianbanData = data; // 保存数据供切换使用
    renderLianbanChart(currentLianbanView);
}

// 渲染连板图表
function renderLianbanChart(viewType) {
    if (!currentLianbanData || currentLianbanData.length === 0) return;

    const dates = currentLianbanData.map(item => formatDate(item.date));
    let levels, getColorFunc;

    if (viewType === 'main') {
        // 主要级别：首板到3进4
        levels = ['首板', '1进2', '2进3', '3进4'];
        getColorFunc = getMainLevelColor;
    } else {
        // 高级别：3进4到9进10
        levels = ['3进4', '5进6', '9进10'];
        getColorFunc = getHighLevelColor;
    }

    const datasets = levels.map((level, index) => {
        const rawData = currentLianbanData.map(item => item.progress[level]?.percentage || 0);

        // 对100%的数据点进行处理，避免图表失真
        const processedData = rawData.map(value => {
            if (value === 100) {
                return 95; // 将100%显示为95%，保持图表比例
            }
            return value;
        });

        return {
            label: level,
            data: processedData,
            borderColor: getColorFunc(index),
            backgroundColor: getColorFunc(index, 0.1),
            fill: false,
            tension: 0.3, // 增加平滑度
            borderWidth: 2,
            pointRadius: 2,
            pointHoverRadius: 4
        };
    });

    lianbanChart.data.labels = dates;
    lianbanChart.data.datasets = datasets;
    lianbanChart.update();
}

// 为主要级别定义更清晰的颜色
function getMainLevelColor(index, alpha = 1) {
    const colors = [
        `rgba(220, 53, 69, ${alpha})`,   // 首板 - 红色
        `rgba(40, 167, 69, ${alpha})`,   // 1进2 - 绿色
        `rgba(0, 123, 255, ${alpha})`,   // 2进3 - 蓝色
        `rgba(255, 193, 7, ${alpha})`    // 3进4 - 黄色
    ];
    return colors[index % colors.length];
}

// 为高级别定义颜色
function getHighLevelColor(index, alpha = 1) {
    const colors = [
        `rgba(255, 193, 7, ${alpha})`,   // 3进4 - 黄色
        `rgba(111, 66, 193, ${alpha})`,  // 5进6 - 紫色
        `rgba(255, 99, 132, ${alpha})`   // 9进10 - 粉色
    ];
    return colors[index % colors.length];
}

// 切换连板视图
function switchLianbanView(viewType) {
    currentLianbanView = viewType;

    // 更新按钮状态
    document.getElementById('btn-main-levels').classList.toggle('active', viewType === 'main');
    document.getElementById('btn-high-levels').classList.toggle('active', viewType === 'high');

    // 重新渲染图表
    renderLianbanChart(viewType);
}

// 更新现代金字塔
function updateModernPyramid(progressData, stocksData) {
    if (!progressData || progressData.length === 0) {
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    // 获取最新一天的进度数据
    const latestProgress = progressData[progressData.length - 1].progress;

    // 定义所有可能的级别顺序（从高到低）
    const allLevels = ['9进10', '8进9', '7进8', '6进7', '5进6', '4进5', '3进4', '2进3', '1进2', '首板'];

    // 筛选出实际存在数据的级别
    const existingLevels = allLevels.filter(level => {
        const progressInfo = latestProgress[level];
        const stocks = stocksData ? (stocksData[level] || []) : [];
        return progressInfo && (progressInfo.total > 0 || stocks.length > 0);
    });

    // 如果没有任何级别有数据，显示提示信息
    if (existingLevels.length === 0) {
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    // 更新成功率显示
    updateSuccessRatesDisplay(latestProgress, existingLevels);

    // 动态调整金字塔高度分配
    const containerElement = document.getElementById('lianban-pyramid-visual').parentElement;
    const totalHeight = containerElement.clientHeight - 40; // 减去padding
    const levelCount = existingLevels.length;

    // 计算总股票数量
    const totalStocks = existingLevels.reduce((total, level) => {
        const stocks = stocksData ? (stocksData[level] || []) : [];
        return total + stocks.length;
    }, 0);

    let html = '<div class="lianban-text-display">';

    existingLevels.forEach((level, index) => {
        const progressInfo = latestProgress[level];
        const stocks = stocksData ? (stocksData[level] || []) : [];

        const successRate = progressInfo.percentage;
        const stockCount = stocks.length;
        const successCount = progressInfo.success;
        const totalCount = progressInfo.total;

        // 计算该级别应占的高度比例 - 按个股数量分配
        const heightRatio = totalStocks > 0 ? stockCount / totalStocks : 1 / levelCount;
        const minHeight = 50; // 最小高度
        const maxHeight = Math.floor(totalHeight * 0.6); // 最大高度不超过总高度的60%

        // 基础高度按股票数量比例分配，确保总和不超过容器高度
        const proportionalHeight = Math.floor(totalHeight * heightRatio);
        const calculatedHeight = Math.max(minHeight, Math.min(maxHeight, proportionalHeight));

        // 生成股票显示 - 每行4个
        let stocksHtml = '';
        let allStocksTooltip = '';

        if (stocks.length > 0) {
            // 按状态排序，成功的在前面
            const sortedStocks = stocks.sort((a, b) => {
                if (a.status === '成' && b.status !== '成') return -1;
                if (a.status !== '成' && b.status === '成') return 1;
                return parseFloat(b.change_percent) - parseFloat(a.change_percent);
            });

            // 动态计算显示股票数量
            const maxRows = Math.floor(calculatedHeight / 25); // 每行约25px高度
            const maxDisplayStocks = maxRows * 4;

            // 确保至少显示所有股票（如果数量不多的话）
            let finalMaxStocks = maxDisplayStocks;
            if (sortedStocks.length <= 10) {
                // 如果股票数量少于等于10只，全部显示
                finalMaxStocks = sortedStocks.length;
            } else if (sortedStocks.length <= 20 && maxDisplayStocks < sortedStocks.length) {
                // 如果股票数量在10-20之间，且计算的显示数量不足，适当增加
                finalMaxStocks = Math.min(sortedStocks.length, maxDisplayStocks + 8);
            }

            const displayStocks = sortedStocks.slice(0, finalMaxStocks);

            stocksHtml = displayStocks.map(stock => {
                const isSuccess = stock.status === '成';
                const isFailed = stock.status === '炸';
                const changePercent = parseFloat(stock.change_percent);
                let colorClass = 'text-danger'; // 默认失败红色
                if (isSuccess) {
                    colorClass = 'text-success'; // 成功绿色
                } else if (isFailed) {
                    colorClass = 'text-warning'; // 炸板黄色
                }
                const sign = changePercent >= 0 ? '+' : '';
                const statusText = isSuccess ? '成功' : (isFailed ? '炸板' : '失败');
                return `<span class="stock-item ${colorClass}" title="${stock.name} ${sign}${changePercent.toFixed(2)}% (${statusText})" onclick="showStockDetail('${stock.name}', '${stock.code}', '${stock.change_percent}', '${stock.industry || ''}', '${stock.status}')">${stock.name} ${sign}${changePercent.toFixed(2)}%</span>`;
            }).join('');

            // 生成所有股票的提示信息
            allStocksTooltip = sortedStocks.map(stock => {
                const changePercent = parseFloat(stock.change_percent);
                const sign = changePercent >= 0 ? '+' : '';
                const isSuccess = stock.status === '成';
                const isFailed = stock.status === '炸';
                const statusText = isSuccess ? '成功' : (isFailed ? '炸板' : '失败');
                return `${stock.name} ${sign}${changePercent.toFixed(2)}% (${statusText})`;
            }).join('\\n');
        }

        html += `
            <div class="lianban-level-row" style="min-height: ${calculatedHeight}px;" onclick="showLevelDetail('${level}', ${successRate}, ${successCount}, ${totalCount}, ${stockCount})">
                <div class="level-info">
                    <span class="level-badge level-${level.replace('进', '-')}">${level}</span>
                    <span class="level-stats" title="${allStocksTooltip}">${successCount}/${totalCount}</span>
                </div>
                <div class="stocks-list" style="max-height: ${calculatedHeight - 20}px;">
                    ${stocksHtml}
                </div>
            </div>
        `;
    });

    html += '</div>';

    document.getElementById('lianban-pyramid-visual').innerHTML = html;

    // 更新日期显示
    const today = new Date().toLocaleDateString('zh-CN');
    document.getElementById('pyramid-date').textContent = today;
}

// 更新成功率显示
function updateSuccessRatesDisplay(latestProgress, levelOrder) {
    levelOrder.forEach(level => {
        const progressInfo = latestProgress[level];
        if (!progressInfo) return;

        const successRate = progressInfo.percentage || 0;

        const rateElement = document.querySelector(`.success-rate-item[data-level="${level}"] .rate-value`);
        if (rateElement) {
            rateElement.textContent = successRate > 0 ? `${successRate}%` : '-';
        }
    });
}

// 原有的金字塔函数保持兼容
function updateLianbanPyramidVisual(progressData, stocksData) {
    if (!progressData || progressData.length === 0) {
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    // 获取最新一天的进度数据
    const latestProgress = progressData[progressData.length - 1].progress;

    // 定义所有可能的级别顺序（从高到低）
    const allLevels = ['9进10', '8进9', '7进8', '6进7', '5进6', '4进5', '3进4', '2进3', '1进2', '首板'];

    // 筛选出实际存在数据的级别
    const levelOrder = allLevels.filter(level => {
        const progressInfo = latestProgress[level];
        const stocks = stocksData ? (stocksData[level] || []) : [];
        return progressInfo && (progressInfo.total > 0 || stocks.length > 0);
    });

    // 如果没有任何级别有数据，显示提示信息
    if (levelOrder.length === 0) {
        document.getElementById('lianban-pyramid-visual').innerHTML = '<p class="text-muted text-center">暂无连板数据</p>';
        return;
    }

    let html = '<div class="pyramid-content">';

    levelOrder.forEach((level, index) => {
        const progressInfo = latestProgress[level];
        const stocks = stocksData ? (stocksData[level] || []) : [];

        if (!progressInfo) return;

        const successRate = progressInfo.percentage;
        const stockCount = stocks.length;
        const successCount = progressInfo.success;
        const totalCount = progressInfo.total;

        // 交替显示成功率在左右两侧
        const ratePosition = index % 2 === 0 ? 'success-rate-left' : 'success-rate-right';

        // 生成股票显示
        let stocksHtml = '';
        if (stocks.length > 0) {
            // 按状态排序，成功的在前面
            const sortedStocks = stocks.sort((a, b) => {
                if (a.status === '成' && b.status !== '成') return -1;
                if (a.status !== '成' && b.status === '成') return 1;
                return parseFloat(b.change_percent) - parseFloat(a.change_percent);
            });

            // 动态调整显示股票数量，确保所有级别的股票都能显示
            let maxStocks;
            if (level === '首板') {
                maxStocks = 35;
            } else if (level === '1进2') {
                maxStocks = 30;
            } else if (level === '2进3') {
                maxStocks = 25;
            } else if (level === '3进4') {
                maxStocks = 20;
            } else if (level === '4进5') {
                maxStocks = 15;
            } else if (level === '5进6') {
                maxStocks = 15;
            } else if (level === '6进7' || level === '7进8' || level === '8进9' || level === '9进10') {
                maxStocks = 12;
            } else {
                // 对于更高级别，显示所有股票
                maxStocks = sortedStocks.length;
            }

            // 如果实际股票数量少于限制，显示所有股票
            const displayStocks = sortedStocks.slice(0, Math.min(maxStocks, sortedStocks.length));

            stocksHtml = displayStocks.map(stock => {
                const isSuccess = stock.status === '成';
                const stockClass = isSuccess ? 'success' : 'fail';
                return `<span class="stock-mini ${stockClass}" title="${stock.name} ${stock.change_percent}%">${stock.name}</span>`;
            }).join('');
        }

        html += `
            <div class="pyramid-level pyramid-level-${level}"
                 onclick="showLevelDetail('${level}', ${successRate}, ${successCount}, ${totalCount}, ${stockCount})"
                 title="点击查看${level}详情">
                <div class="success-rate ${ratePosition}">
                    ${successRate}%
                    <br><small>${successCount}/${totalCount}</small>
                </div>
                <div class="level-title">${level}</div>
                <div class="level-stocks">
                    ${stocksHtml}
                </div>
            </div>
        `;
    });

    html += '</div>';

    document.getElementById('lianban-pyramid-visual').innerHTML = html;

    // 更新日期显示
    const today = new Date().toLocaleDateString('zh-CN');
    document.getElementById('pyramid-date').textContent = today;
}

// 显示级别详情
function showLevelDetail(level, successRate, successCount, totalCount, stockCount) {
    const message = `${level}连板详情：

成功率: ${successRate}%
成功数量: ${successCount}只
尝试数量: ${totalCount}只
当前存在: ${stockCount}只股票

${successRate >= 50 ? '✅ 成功率较高，市场情绪良好' : '⚠️ 成功率偏低，需谨慎操作'}`;

    if (typeof showToast !== 'undefined') {
        const toastType = successRate >= 50 ? 'success' : 'warning';
        showToast(`${level}: ${successRate}% (${successCount}/${totalCount})`, toastType);
    } else {
        alert(message);
    }
}

// 显示股票详情
function showStockDetail(name, code, changePercent, industry, status) {
    const statusText = status === '成' ? '成功' : '失败';
    const statusColor = status === '成' ? '#28a745' : '#dc3545';

    const message = `
        <div style="text-align: left;">
            <h6 style="color: ${statusColor}; margin-bottom: 10px;">
                ${name} (${code})
            </h6>
            <p style="margin: 5px 0;"><strong>涨幅:</strong> ${changePercent}%</p>
            <p style="margin: 5px 0;"><strong>行业:</strong> ${industry}</p>
            <p style="margin: 5px 0;"><strong>状态:</strong> <span style="color: ${statusColor};">${statusText}</span></p>
        </div>
    `;

    // 使用简单的alert，也可以替换为更美观的模态框
    if (typeof showToast !== 'undefined') {
        showToast(`${name}: ${changePercent}% (${statusText})`, status === '成' ? 'success' : 'danger');
    } else {
        alert(`${name} (${code})\n涨幅: ${changePercent}%\n行业: ${industry}\n状态: ${statusText}`);
    }
}

// 更新市场数据条
function updateMarketBars(data) {
    if (!data || data.length === 0) {
        document.getElementById('market-bars').innerHTML = '<div class="text-center text-muted">暂无数据</div>';
        document.getElementById('volume-info').innerHTML = '<div class="text-center text-muted">暂无数据</div>';
        return;
    }

    const latest = data[data.length - 1];
    if (!latest) {
        document.getElementById('market-bars').innerHTML = '<div class="text-center text-muted">数据格式错误</div>';
        return;
    }

    const riseCount = latest.rise_count || 0;
    const fallCount = latest.fall_count || 0;
    const flatCount = latest.flat_count || 0;
    const total = riseCount + fallCount + flatCount;

    if (total === 0) {
        document.getElementById('market-bars').innerHTML = '<div class="text-center text-muted">暂无交易数据</div>';
        return;
    }

    const risePercent = (riseCount / total * 100).toFixed(1);
    const fallPercent = (fallCount / total * 100).toFixed(1);
    const flatPercent = (flatCount / total * 100).toFixed(1);

    const marketBarsHtml = `
        <span class="bar-label-left rise">涨 ${riseCount}</span>
        <div class="bar-container-horizontal">
            <div class="bar-segment-horizontal rise" style="width: ${risePercent}%"></div>
            <div class="bar-segment-horizontal flat" style="width: ${flatPercent}%"></div>
            <div class="bar-segment-horizontal fall" style="width: ${fallPercent}%"></div>
        </div>
        <span class="bar-label-right fall">跌 ${fallCount}</span>
    `;

    document.getElementById('market-bars').innerHTML = marketBarsHtml;

    // 更新成交金额
    const volume = latest.volume || 0;
    const volumeHtml = `
        <div class="volume-value">${latest.volume_formatted || formatAmount(volume)}</div>
        <div class="volume-label">成交金额</div>
    `;
    document.getElementById('volume-info').innerHTML = volumeHtml;
}

// 更新涨跌停数据条
function updateLimitBars(data) {
    if (!data || data.length === 0) {
        document.getElementById('limit-bars').innerHTML = '<div class="text-center text-muted" style="font-size: 0.7rem;">暂无数据</div>';
        return;
    }

    const latest = data[data.length - 1];
    if (!latest) {
        document.getElementById('limit-bars').innerHTML = '<div class="text-center text-muted" style="font-size: 0.7rem;">数据格式错误</div>';
        return;
    }

    const limitUp = latest.limit_up || latest.limit_up_total || 0;
    const limitDown = latest.limit_down || latest.limit_down_total || 0;
    const total = limitUp + limitDown;

    let limitUpPercent = 50;
    let limitDownPercent = 50;

    if (total > 0) {
        limitUpPercent = (limitUp / total * 100);
        limitDownPercent = (limitDown / total * 100);
    }

    const limitBarsHtml = `
        <span class="bar-label-left rise">涨停 ${limitUp}</span>
        <div class="bar-container-horizontal">
            <div class="bar-segment-horizontal limit-up" style="width: ${limitUpPercent}%"></div>
            <div class="bar-segment-horizontal limit-down" style="width: ${limitDownPercent}%"></div>
        </div>
        <span class="bar-label-right fall">跌停 ${limitDown}</span>
    `;

    document.getElementById('limit-bars').innerHTML = limitBarsHtml;
}
