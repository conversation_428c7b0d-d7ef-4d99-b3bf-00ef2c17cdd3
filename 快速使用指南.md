# A股数据定时采集脚本 - 快速使用指南

## 🚀 快速开始

### 1. 检查文件
确保以下文件在同一目录中：
- ✅ `dabanke_success.py` - 大板客数据脚本
- ✅ `wencai_formatted.py` - 问财数据脚本
- ✅ `stock_data_scheduler.py` - 主调度脚本（包含依赖检查）

### 2. 安装依赖
```bash
pip install requests beautifulsoup4 pywencai pandas
```

### 3. 启动脚本
```bash
python stock_data_scheduler.py
```

## ⏰ 执行时间规则

| 时间段         | 执行频率 | 说明                               |
| -------------- | -------- | ---------------------------------- |
| **交易时间**   | 每5分钟  | 周一至周五 9:30-11:30, 13:00-15:00 |
| **非交易时间** | 每30分钟 | 其他所有时间                       |

## 📊 运行效果

脚本会：
1. **立即执行一次** 数据采集
2. **自动判断** 当前是否为交易时间
3. **按规则定时执行** 两个数据脚本
4. **记录日志** 到 `logs/` 目录
5. **保存数据** 到相应目录

## 🛑 停止脚本

按 `Ctrl+C` 优雅退出

## 📁 生成的文件

```
├── logs/                          # 日志目录
│   └── scheduler_2025-08-12.log   # 按日期的日志文件
├── dabanke_data/                  # 大板客数据
│   └── dabanke_20250812.json     # 连板数据
└── market_data/                   # 市场数据
    └── market_data_2025-08-12.json # 问财数据
```

## 🔧 高级用法

### 作为系统服务运行（Linux）

1. 创建系统服务文件：
```bash
sudo nano /etc/systemd/system/stock-scheduler.service
```

2. 添加以下内容（修改路径为实际路径）：
```ini
[Unit]
Description=A股数据定时采集服务
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/your/stock_info
ExecStart=/usr/bin/python3 /path/to/your/stock_info/stock_data_scheduler.py
Restart=always
RestartSec=10

# 环境变量
Environment=PYTHONPATH=/path/to/your/stock_info
Environment=PYTHONUNBUFFERED=1

# 日志设置
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

3. 启用并启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable stock-scheduler
sudo systemctl start stock-scheduler
```

4. 查看服务状态：
```bash
sudo systemctl status stock-scheduler
```

### 查看实时日志
```bash
tail -f logs/scheduler_$(date +%Y-%m-%d).log
```

## ❓ 常见问题

**Q: 脚本启动后没有立即执行？**
A: 脚本启动时会立即执行一次，然后按时间规则定时执行。

**Q: 如何修改执行间隔？**
A: 编辑 `stock_data_scheduler.py`，修改以下参数：
```python
# 交易时间间隔（秒）
return time_diff.total_seconds() >= 300  # 5分钟

# 非交易时间间隔（秒）  
return time_diff.total_seconds() >= 1800  # 30分钟
```

**Q: 网络错误怎么办？**
A: 脚本有自动重试机制，网络错误不会导致程序崩溃，会在日志中记录错误信息。

**Q: 如何查看详细错误？**
A: 查看日志文件：`logs/scheduler_日期.log`

## 📞 技术支持

如有问题，请检查：
1. 网络连接是否正常
2. 依赖包是否正确安装
3. 日志文件中的错误信息
4. 文件权限是否正确
