# A股数据定时采集脚本 - 使用说明

## 📋 概述

这是一个自动化的A股数据采集脚本，会根据股市交易时间智能调整执行频率：
- **交易时间**（周一至周五 9:30-11:30, 13:00-15:00）：每 **5分钟** 执行一次
- **非交易时间**：每 **30分钟** 执行一次

## 📁 文件结构

```
stock_info/
├── stock_data_scheduler.py    # 主脚本（包含所有功能）
├── dabanke_success.py         # 大板客数据采集
├── wencai_formatted.py        # 问财数据采集
├── 快速使用指南.md            # 快速上手指南
├── README_scheduler.md        # 详细说明文档
├── logs/                      # 日志目录
│   └── scheduler_YYYY-MM-DD.log
├── dabanke_data/             # 大板客数据
│   └── dabanke_YYYYMMDD.json
└── market_data/              # 问财数据
    └── market_data_YYYY-MM-DD.json
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests beautifulsoup4 pywencai pandas
```

### 2. 启动脚本
```bash
python stock_data_scheduler.py
```

### 3. 停止脚本
按 `Ctrl+C` 优雅退出

## ✨ 功能特点

- ✅ **智能时间判断** - 自动识别交易时间和非交易时间
- ✅ **依赖自动检查** - 启动前检查必要文件和Python包
- ✅ **异常处理** - 一个脚本出错不影响另一个
- ✅ **详细日志** - 记录所有执行情况和错误信息
- ✅ **优雅退出** - 支持 Ctrl+C 安全停止
- ✅ **立即执行** - 启动时立即执行一次数据采集

## 📊 执行规则

| 时间类型 | 执行间隔 | 说明 |
|---------|---------|------|
| 交易时间 | 5分钟 | 周一至周五 9:30-11:30, 13:00-15:00 |
| 非交易时间 | 30分钟 | 其他所有时间（包括周末、节假日） |

## 🔧 高级配置

### 修改执行间隔

编辑 `stock_data_scheduler.py` 文件中的时间参数：

```python
# 在 should_execute 方法中修改
if self.is_trading_time(now):
    # 交易时间：每5分钟执行一次
    return time_diff.total_seconds() >= 300  # 改为其他秒数
else:
    # 非交易时间：每30分钟执行一次
    return time_diff.total_seconds() >= 1800  # 改为其他秒数
```

### 作为系统服务运行

创建系统服务文件：
```bash
sudo nano /etc/systemd/system/stock-scheduler.service
```

添加配置（修改路径）：
```ini
[Unit]
Description=A股数据定时采集服务
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/your/stock_info
ExecStart=/usr/bin/python3 /path/to/your/stock_info/stock_data_scheduler.py
Restart=always
RestartSec=10
Environment=PYTHONPATH=/path/to/your/stock_info
Environment=PYTHONUNBUFFERED=1
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable stock-scheduler
sudo systemctl start stock-scheduler
```

## 📝 日志查看

### 实时查看日志
```bash
tail -f logs/scheduler_$(date +%Y-%m-%d).log
```

### 查看系统服务日志
```bash
sudo journalctl -u stock-scheduler -f
```

## ❓ 常见问题

**Q: 脚本启动失败？**
A: 检查依赖是否安装完整，确保 `dabanke_success.py` 和 `wencai_formatted.py` 在同一目录。

**Q: 网络连接错误？**
A: 脚本有自动重试机制，偶尔的网络错误不会导致程序崩溃。

**Q: 如何确认脚本正在运行？**
A: 查看日志文件或控制台输出，会显示执行时间和结果。

**Q: 数据保存在哪里？**
A: 
- 大板客数据：`dabanke_data/dabanke_YYYYMMDD.json`
- 问财数据：`market_data/market_data_YYYY-MM-DD.json`
- 日志文件：`logs/scheduler_YYYY-MM-DD.log`

## 🛠️ 故障排除

1. **检查文件权限** - 确保有读写权限
2. **检查网络连接** - 确保能访问相关网站
3. **查看详细日志** - 日志文件包含详细错误信息
4. **重启脚本** - 简单的重启通常能解决临时问题

## 📞 技术支持

如遇问题，请：
1. 查看日志文件获取详细错误信息
2. 确认网络连接和依赖安装
3. 检查文件权限和路径配置

---

**注意**：此脚本需要稳定的网络连接和足够的磁盘空间来存储数据和日志文件。建议在服务器环境中长期运行。
